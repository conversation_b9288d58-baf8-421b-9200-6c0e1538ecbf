import 'dart:async';

import 'package:dio/dio.dart';
import 'package:easy_debounce/easy_throttle.dart';
import 'package:flutter_audio_room/core/mixins/exception_handler_mixin.dart';
import 'package:flutter_audio_room/core/router/app_router.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/app_settings/screen/app_settings_screen.dart';
import 'package:flutter_audio_room/flavors.dart';
import 'package:flutter_audio_room/main.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/data/remote/remote.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_audio_room/shared/globals.dart';

class TokenRefreshService extends NetworkService with ExceptionHandlerMixin {
  final Dio _dio;
  final StorageService storageService;
  final IDeviceInfoService deviceInfoService;
  final IPackageInfoService packageInfoService;

  // 静态Completer变量，用于控制并发
  static Completer<ResultWithData<String>>? _refreshCompleter;

  final List<String> _refreshTokenExpiredCode = [
    'refresh.token.replaced',
    'token.expired'
  ];

  TokenRefreshService(this._dio, this.storageService, this.deviceInfoService,
      this.packageInfoService) {
    _dio.options = BaseOptions(
      baseUrl: baseUrl,
      headers: headers,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 10),
    );
  }

  void toLogin({String? message}) {
    LogUtils.d(
      'TokenRefreshService: Token refresh failed, redirecting to login',
      tag: 'TokenRefreshService',
    );
    EasyThrottle.throttle(
      'TokenRefreshService.toLogin',
      const Duration(seconds: 1),
      () async {
        final context = AppRouter.navigatorKey.currentContext;
        if (context == null) return;

        final logoutController = LogoutController(providerContainer, context);
        logoutController.logout(message: message);
      },
    );
  }

  /// 刷新令牌方法，添加了并发控制
  /// 同一时间只能有一个刷新请求在执行
  Future<ResultWithData<String>> refreshToken({int maxRetryCount = 3}) async {
    // 如果已经有一个刷新请求在执行，直接返回该请求的结果
    if (_refreshCompleter != null && !_refreshCompleter!.isCompleted) {
      LogUtils.d(
        'TokenRefreshService: Token refresh already in progress, waiting for completion',
        tag: 'TokenRefreshService',
      );
      return _refreshCompleter!.future;
    }

    // 创建一个新的 Completer
    _refreshCompleter = Completer<ResultWithData<String>>();

    // 使用循环而不是递归来处理重试
    int remainingRetries = maxRetryCount;

    while (true) {
      try {
        final result = await _performTokenRefresh();

        // 检查是否需要重试
        final shouldRetry = result.fold(
          (exception) =>
              _shouldRetryForException(exception) && remainingRetries > 0,
          (token) => token.isEmpty && remainingRetries > 0,
        );

        if (shouldRetry) {
          remainingRetries--;
          LogUtils.d(
            'Token refresh failed, retrying... ($remainingRetries retries left)',
            tag: 'TokenRefreshService',
          );

          // 通知等待的请求当前尝试失败，但不清空Completer
          // if (_refreshCompleter != null && !_refreshCompleter!.isCompleted) {
          //   _refreshCompleter!.complete(result);
          //   _refreshCompleter = Completer<ResultWithData<String>>();
          // }

          await Future.delayed(const Duration(seconds: 1));
          continue;
        }

        // 成功或不需要重试，完成刷新
        _completeRefresh(result);
        return result;
      } catch (e) {
        final result = Left<AppException, String>(AppException(
          message: 'Unexpected error during token refresh: ${e.toString()}',
          statusCode: 500,
          identifier: 'TokenRefreshService',
        ));
        _completeRefresh(result);
        return result;
      }
    }
  }

  /// 执行实际的token刷新请求
  Future<ResultWithData<String>> _performTokenRefresh() async {
    final refreshTokenString = storageService.refreshToken;
    if (refreshTokenString.isEmpty) {
      toLogin();
      return const Left<AppException, String>(AppException(
        message: 'Refresh token not found',
        statusCode: 404,
        identifier: 'TokenRefreshService',
      ));
    }

    final res = await post(
      '/profile/auth/refreshToken',
      data: {'refreshToken': refreshTokenString},
    );

    return res.fold(
      (exception) {
        if (_refreshTokenExpiredCode.contains(exception.identifier)) {
          toLogin();
        }
        return Left<AppException, String>(exception);
      },
      (response) async {
        final accessToken = response['accessToken'] ?? '';
        if (accessToken.isEmpty) {
          return const Left<AppException, String>(AppException(
            message: 'Access token is empty',
            statusCode: 404,
            identifier: 'TokenRefreshService',
          ));
        }

        await storageService.setAccessToken(accessToken);
        return Right(accessToken);
      },
    );
  }

  /// 判断是否应该为特定异常重试
  bool _shouldRetryForException(AppException exception) {
    // 不重试的情况：refresh token过期或被替换
    if (_refreshTokenExpiredCode.contains(exception.identifier)) {
      return false;
    }
    // 其他错误可以重试
    return true;
  }
  
  /// 完成刷新过程并完成 Completer
  void _completeRefresh(ResultWithData<String> result) {
    if (_refreshCompleter != null && !_refreshCompleter!.isCompleted) {
      _refreshCompleter!.complete(result);
    }
    _refreshCompleter = null;
  }

  @override
  String get baseUrl => F.connectionString;

  @override
  Future<ResultWithData<Map<String, dynamic>>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) {
    final res = handleException(
      () => _dio.get(
        endpoint,
        queryParameters: queryParameters,
      ),
      endpoint: endpoint,
    );
    return res;
  }

  @override
  Map<String, Object> get headers => {
        'accept': 'application/json',
        'content-type': 'application/json',
        'GK-platform': deviceInfoService.platform,
        'GK-app-version': packageInfoService.version,
      };

  @override
  Future<ResultWithData<Map<String, dynamic>>> post(String endpoint,
      {Map<String, dynamic>? data}) {
    final res = handleException(
      () => _dio.post(
        endpoint,
        data: data,
      ),
      endpoint: endpoint,
    );
    return res;
  }

  @override
  Future<ResultWithData<Map<String, dynamic>>> uploadFile(
    String endpoint, {
    required String filePath,
    Map<String, dynamic>? queryParameters,
    String? filename,
    String formName = 'file',
    Map<String, dynamic>? extraData,
    void Function(int count, int total)? onSendProgress,
  }) {
    throw UnimplementedError();
  }

  @override
  Map<String, dynamic>? updateHeader(Map<String, dynamic> data) {
    final header = {...headers, ...data};
    if (!kTestMode) {
      _dio.options.headers = header;
    }
    return header;
  }
}
