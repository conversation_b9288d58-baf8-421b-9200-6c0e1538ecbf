import 'dart:collection';

import 'package:dio/dio.dart';
import 'package:flutter_audio_room/core/mixins/exception_handler_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/utils/callback_handlers.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/services/token_refresh/request_interceptor.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/data/remote/network_service.dart';
import 'package:flutter_audio_room/shared/data/remote/retry_config.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/globals.dart';
import 'package:path/path.dart' as path;
import 'package:synchronized/synchronized.dart';

class DioNetworkService extends NetworkService with ExceptionHandlerMixin {
  final Dio dio;
  final IDeviceInfoService _deviceInfoService;
  final IPackageInfoService _packageInfoService;
  final StorageService _storageService;
  final RequestInterceptor _requestInterceptor;
  final String _baseUrl;
  
  // Add cancel token management
  final Map<String, CancelToken> _cancelTokens = {};
  final _lock = Lock();
  final _requestQueue = CallbackQueue();

  DioNetworkService(
    this.dio,
    this._deviceInfoService,
    this._packageInfoService,
    this._storageService,
    this._requestInterceptor,
    this._baseUrl,
  ) {
    dio.options = BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 3),
      receiveTimeout: const Duration(seconds: 6),
      sendTimeout: const Duration(seconds: 3),
    );

    // Add retry interceptor first (should be before other interceptors)
    final retryConfig = RetryConfig.fromStrategy(RetryStrategy.defaultStrategy);
    dio.interceptors.add(retryConfig.createInterceptor(dio));

    // Add token refresh interceptor
    dio.interceptors.add(_requestInterceptor);

    // Add request interceptor to dynamically set headers
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        options.headers = headers;
        return handler.next(options);
      },
    ));
  }

  @override
  String get baseUrl => _baseUrl;

  @override
  Map<String, Object> get headers {
    final accessToken = _storageService.accessToken;
    final headers = {
      'accept': 'application/json',
      'content-type': 'application/json',
      'GK-platform': _deviceInfoService.platform,
      'GK-app-version': _packageInfoService.buildNumber,
    };

    return accessToken.isNotEmpty
        ? {
            ...headers,
            'Authorization': 'Bearer $accessToken',
          }
        : headers;
  }

  @override
  Map<String, dynamic>? updateHeader(Map<String, dynamic> data) {
    final header = {...headers, ...data};
    if (!kTestMode) {
      dio.options.headers = header;
    }
    return header;
  }

  /// Get or create a CancelToken for the given endpoint
  Future<CancelToken> _getOrCreateCancelToken(String endpoint) async {
    return await _lock.synchronized(() {
      if (!_cancelTokens.containsKey(endpoint)) {
        _cancelTokens[endpoint] = CancelToken();
      }
      return _cancelTokens[endpoint]!;
    });
  }

  @override
  Future<ResultWithData<Map<String, dynamic>>> post(String endpoint,
      {Map<String, dynamic>? data}) async {
    final cancelToken = await _getOrCreateCancelToken(endpoint);
    try {
      final res = await handleException(
        () => dio.post(
          endpoint,
          data: data,
          cancelToken: cancelToken,
        ),
        endpoint: endpoint,
      );
      return res;
    } finally {
      await _lock.synchronized(() {
        _cancelTokens.remove(endpoint);
      });
    }
  }

  @override
  Future<ResultWithData<Map<String, dynamic>>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    final cancelToken = await _getOrCreateCancelToken(endpoint);
    try {
      final res = await handleException(
        () => dio.get(
          endpoint,
          queryParameters: queryParameters,
          cancelToken: cancelToken,
        ),
        endpoint: endpoint,
      );
      return res;
    } finally {
      await _lock.synchronized(() {
        _cancelTokens.remove(endpoint);
      });
    }
  }
  
  @override
  Future<ResultWithData<Map<String, dynamic>>> uploadFile(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    required String filePath,
    String? filename,
    String formName = 'file',
    Map<String, dynamic>? extraData,
    void Function(int count, int total)? onSendProgress,
  }) async {
    final cancelToken = await _getOrCreateCancelToken(endpoint);
    try {
      // 获取文件名
      final actualFilename = filename ?? path.basename(filePath);

      // 创建FormData
      final Map<String, dynamic> formDataMap = {
        formName: await MultipartFile.fromFile(
          filePath,
          filename: actualFilename,
        ),
      };

      // 添加额外数据
      if (extraData != null) {
        formDataMap.addAll(extraData);
      }

      final formData = FormData.fromMap(formDataMap);

      // 临时修改header以支持文件上传
      final originalContentType = dio.options.headers['content-type'];
      dio.options.headers['content-type'] = 'multipart/form-data';

      final res = await handleException(
        () => dio.post(
          endpoint,
          data: formData,
          queryParameters: queryParameters,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
        ),
        endpoint: endpoint,
      );

      // 恢复原始header
      dio.options.headers['content-type'] = originalContentType;

      return res;
    } finally {
      await _lock.synchronized(() {
        _cancelTokens.remove(endpoint);
      });
    }
  }

  /// Cancel all ongoing requests
  @override
  Future<void> cancelAll() async {
    await _lock.synchronized(() {
      for (final token in _cancelTokens.values) {
        if (!token.isCancelled) {
          token.cancel('User cancelled all requests');
        }
      }
      _cancelTokens.clear();
    });
  }

  /// Cancel request for specific endpoint
  @override
  Future<void> cancelRequest(String endpoint) async {
    await _lock.synchronized(() {
      final token = _cancelTokens[endpoint];
      if (token != null && !token.isCancelled) {
        token.cancel('User cancelled request for $endpoint');
        _cancelTokens.remove(endpoint);
      }
    });
  }
}
