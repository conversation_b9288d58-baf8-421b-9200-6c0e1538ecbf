/// 业务错误码常量
/// 
/// 统一管理项目中使用的各种业务错误码，避免硬编码和重复定义
class BusinessErrorCodes {
  BusinessErrorCodes._();

  // ==================== 认证相关错误码 ====================
  
  /// Token已过期
  static const String tokenExpired = 'token.expired';
  
  /// Token无效
  static const String tokenInvalid = 'token.invalid';
  
  /// Access token被替换（在其他设备登录）
  static const String accessTokenReplaced = 'access.token.replaced';

  // ==================== 账户限制相关错误码 ====================
  
  /// 账户被封禁
  static const String accountBanned = 'account.banned';
  
  /// 账户被限制
  static const String accountRestricted = 'account.restricted';
  
  /// 用户被封禁（来自RestrictType）
  static const String userBanned = 'user.banned';
  
  /// 用户登录被惩罚（来自RestrictType）
  static const String userLoginPunished = 'user.login.punished';

  // ==================== 惩罚相关错误码 ====================
  
  /// 即时语音通话惩罚（来自PunishmentType）
  static const String instantVoiceCallPunishment = 'instant.voice.call.punishment';
  
  /// 即时聊天惩罚（来自PunishmentType）
  static const String instantChatPunishment = 'instant.chat.punishment';
  
  /// 音频房间创建惩罚（来自PunishmentType）
  static const String audioRoomCreatePunishment = 'audio.room.create.punishment';
  
  /// 音频房间加入惩罚（来自PunishmentType）
  static const String audioRoomJoinPunishment = 'audio.room.join.punishment';

  // ==================== 权限相关错误码 ====================
  
  /// 权限被拒绝
  static const String permissionDenied = 'permission.denied';
  
  /// 验证失败
  static const String validationFailed = 'validation.failed';

  // ==================== 错误码分组 ====================
  
  /// 需要刷新Token的错误码
  static const Set<String> tokenRefreshCodes = {
    tokenExpired,
    tokenInvalid,
  };
  
  /// 需要退出登录的错误码
  static const Set<String> logoutCodes = {
    accessTokenReplaced,
  };
  
  /// 账户限制相关的错误码（来自RestrictType）
  static const Set<String> restrictCodes = {
    userBanned,
    userLoginPunished,
  };
  
  /// 惩罚相关的错误码（来自PunishmentType）
  static const Set<String> punishmentCodes = {
    instantVoiceCallPunishment,
    instantChatPunishment,
    audioRoomCreatePunishment,
    audioRoomJoinPunishment,
  };
  
  /// 不应该重试的业务错误码
  /// 这些错误码表示业务逻辑错误，重试不会解决问题
  static const Set<String> nonRetryableBusinessCodes = {
    // 认证相关
    accessTokenReplaced,
    tokenExpired,
    tokenInvalid,
    
    // 账户限制相关
    accountBanned,
    accountRestricted,
    userBanned,
    userLoginPunished,
    
    // 权限相关
    permissionDenied,
    validationFailed,
    
    // 惩罚相关（这些通常也不应该重试）
    instantVoiceCallPunishment,
    instantChatPunishment,
    audioRoomCreatePunishment,
    audioRoomJoinPunishment,
  };

  // ==================== 工具方法 ====================
  
  /// 检查是否是需要刷新Token的错误码
  static bool isTokenRefreshCode(String code) {
    return tokenRefreshCodes.contains(code);
  }
  
  /// 检查是否是需要退出登录的错误码
  static bool isLogoutCode(String code) {
    return logoutCodes.contains(code);
  }
  
  /// 检查是否是账户限制相关的错误码
  static bool isRestrictCode(String code) {
    return restrictCodes.contains(code);
  }
  
  /// 检查是否是惩罚相关的错误码
  static bool isPunishmentCode(String code) {
    return punishmentCodes.contains(code);
  }
  
  /// 检查是否是不应该重试的业务错误码
  static bool isNonRetryableBusinessCode(String code) {
    return nonRetryableBusinessCodes.contains(code);
  }
  
  /// 获取错误码的描述信息
  static String getErrorDescription(String code) {
    switch (code) {
      case tokenExpired:
        return 'Login expired, please login again';
      case tokenInvalid:
        return 'Access info invalid, please login again';
      case accessTokenReplaced:
        return 'Account logged in on other devices, current session invalid';
      case accountBanned:
        return 'Account banned';
      case accountRestricted:
        return 'Account restricted';
      case userBanned:
        return 'User banned';
      case userLoginPunished:
        return 'User login punished';
      case permissionDenied:
        return 'Permission denied';
      case validationFailed:
        return 'Validation failed';
      case instantVoiceCallPunishment:
        return 'Instant voice call punished';
      case instantChatPunishment:
        return 'Instant chat punished';
      case audioRoomCreatePunishment:
        return 'Audio room create punished';
      case audioRoomJoinPunishment:
        return 'Audio room join punished';
      default:
        return 'Unknown error: $code';
    }
  }
}
